import json
import base64
import gzip

def decrypt_data(encoded_data):
    """解密Base64编码的gzip压缩数据"""
    try:
        print(f"原始数据: {encoded_data}")
        print(f"数据长度: {len(encoded_data)}")
        
        # 解码Base64
        compressed_data = base64.b64decode(encoded_data)
        print(f"Base64解码后长度: {len(compressed_data)}")
        print(f"前20字节: {compressed_data[:20].hex()}")
        
        # 解压gzip
        decompressed_data = gzip.decompress(compressed_data)
        print(f"gzip解压后长度: {len(decompressed_data)}")
        print(f"前20字节: {decompressed_data[:20].hex()}")
        
        # 尝试不同的编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'ascii']
        for encoding in encodings:
            try:
                result = decompressed_data.decode(encoding)
                print(f"成功使用 {encoding} 编码解密")
                return result
            except UnicodeDecodeError as e:
                print(f"{encoding} 编码失败: {e}")
                continue
        
        # 如果所有编码都失败，返回原始字节数据的十六进制表示
        print("所有编码格式都失败，返回十六进制数据:")
        return decompressed_data.hex()
        
    except Exception as e:
        print(f"解密失败: {e}")
        return None

def analyze_costinfo(cost_str):
    """分析costInfo字符串"""
    print(f"\n分析costInfo: {cost_str}")
    
    # 尝试解析为字典格式
    try:
        # 移除外层的大括号并分割
        inner = cost_str.strip('{}')
        parts = inner.split(', ')
        
        cost_dict = {}
        for part in parts:
            key, value = part.split(': ')
            cost_dict[key] = int(value)
        
        print("解析结果:")
        for key, value in cost_dict.items():
            print(f"  {key}: {value}")
            
        return cost_dict
    except Exception as e:
        print(f"解析costInfo失败: {e}")
        return None

def analyze_json_data(json_str):
    """分析JSON数据"""
    print(f"\n分析JSON数据: {json_str}")
    
    try:
        data = json.loads(json_str)
        print("解析结果:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
        return data
    except Exception as e:
        print(f"解析JSON失败: {e}")
        return None

if __name__ == "__main__":
    # 你提供的加密数据
    encrypted_data = "H4sIAAAAAAAA/+NQEmCQmPP2yIVvLEaSXOzFGYlFqZ4pQnyGFqYGBuZGhmYmJsYmpgDIFFGgJgAAAA=="
    
    print("=" * 60)
    print("解密Base64+gzip数据")
    print("=" * 60)
    
    decrypted_result = decrypt_data(encrypted_data)
    
    if decrypted_result:
        print(f"\n解密结果: {decrypted_result}")
        
        # 尝试解析为JSON
        try:
            json_data = json.loads(decrypted_result)
            print("\n成功解析为JSON:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print("\n解密结果不是有效的JSON格式")
    
    print("\n" + "=" * 60)
    print("分析提供的数据参考")
    print("=" * 60)
    
    # 分析costInfo
    cost_info = "{S_A: 1054, S_A_RE: 889, S_A_D: 165}"
    analyze_costinfo(cost_info)
    
    # 分析JSON数据
    json_data = """{
    "decode": true,
    "bizType": 34,
    "photoId": 5193776437779848000,
    "position": 0,
    "extTag": "",
    "extInfoMap": {
        "shareId": "18500721644345"
    }
}"""
    analyze_json_data(json_data)

import json
import base64
import gzip

import requests
from Utils import sig_py, get_sig3, token_py


def decrypt_data(encoded_data):
    """解密Base64编码的gzip压缩数据"""
    try:
        # 解码Base64
        compressed_data = base64.b64decode(encoded_data)

        # 解压gzip
        decompressed_data = gzip.decompress(compressed_data)

        # 尝试不同的编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        for encoding in encodings:
            try:
                result = decompressed_data.decode(encoding)
                print(f"成功使用 {encoding} 编码解密")
                return result
            except UnicodeDecodeError:
                continue

        # 如果所有编码都失败，返回原始字节数据的十六进制表示
        print("所有编码格式都失败，返回十六进制数据:")
        return decompressed_data.hex()

    except Exception as e:
        print(f"解密失败: {e}")
        return None


# 解密你提供的数据
encrypted_data = "H4sIAAAAAAAA/+NQEmCQmPP2yIVvLEaSXOzFGYlFqZ4pQnyGFqYGBuZGhmYmJsYmpgDIFFGgJgAAAA=="
decrypted_result = decrypt_data(encrypted_data)

print("解密结果:")
print(decrypted_result)
print()

# 尝试解析为JSON
if decrypted_result:
    try:
        json_data = json.loads(decrypted_result)
        print("解析为JSON:")
        print(json.dumps(json_data, indent=2, ensure_ascii=False))
    except json.JSONDecodeError:
        print("解密结果不是有效的JSON格式")
print()

headers = {
    "User-Agent": "kwai-android aegon/3.18.0",
    "Content-Type": "application/x-www-form-urlencoded",
    "Accept-Language": "zh-cn",
    "X-REQUESTID": "175137438817733451",
    "X-Client-Info": "model=SM-S9260;os=Android;nqe-score=34;network=WIFI;signal-strength=4;"
}
cookies = {
    "kuaishou.api_st": "Cg9rdWFpc2hvdS5hcGkuc3QSoAGyQr5_Pxnjcm0SZq8ikMsn3acDSjCQZqq-p9vfzoLnXW5OGHXH4G9LEhXfIKI_LJk2KNPmZYT1tt1l4Q_zET5jJK7sXwoDbjdCA-_YVZ38bHJiMNWYuJyYnkKqrlsRV5IpFHK0MjvPvh1N5Te-DQCl8aFWUf-j7ClLSBwHDnUaCdtfmQ4DQOHfvAt6FemPsnhlecbJEIrxMr0weFsMKR_JGhJvFHrrKslACKKghHE7NlCD-YgiIHn9vQ8gP9PW2smphT9q6vKdBy6RfR4ti1k4lGgSX5NeKAUwAQ",
    "token": "6314caebe22b42dbba9b4185beebffd3-4879352811",
    "region_ticket": "RT_ACF963ADF19921780C6137ED16B00009696EFF3ABCF9B3450BB1868D32A04F64397940DFD"
}


params = {
    "earphoneMode": "1",
    "mod": "Samsung(SM-S9260)",
    "appver": "10.10.10.28300",
    "isp": "CMCC",
    "language": "zh-cn",
    "ud": "3166429095",
    "did_tag": "0",
    "egid": "DFPE6BFB11F8AF9B59EF311E914B3763C6243FCEF5A4D5F36E23934860814118",
    "net": "WIFI",
    "kcv": "1599",
    "app": "0",
    "kpf": "ANDROID_PHONE",
    "bottom_navigation": "false",
    "ver": "10.10",
    "oDid": "ANDROID_752b3dcfda26f942",
    "android_os": "0",
    "boardPlatform": "aosp-user",
    "kpn": "KUAISHOU",
    "androidApiLevel": "28",
    "newOc": "ALI_CPD,666",
    "slh": "0",
    "country_code": "cn",
    "nbh": "0",
    "hotfix_ver": "",
    "did_gt": "1751320183207",
    "keyconfig_state": "2",
    "cdid_tag": "5",
    "sys": "ANDROID_9",
    "max_memory": "192",
    "cold_launch_time_ms": "1751373895175",
    "oc": "ALI_CPD,666",
    "sh": "1920",
    "ddpi": "480",
    "deviceBit": "0",
    "browseType": "4",
    "socName": "Qualcomm MSM8998",
    "is_background": "0",
    "c": "ALI_CPD,666",
    "sw": "1080",
    "ftt": "",
    "abi": "arm32",
    "userRecoBit": "0",
    "device_abi": "",
    "totalMemory": "3945",
    "grant_browse_type": "AUTHORIZED",
    "iuid": "",
    "rdid": "ANDROID_5f011e02f6f2798b",
    "sbh": "72",
    "darkMode": "false",
    "did": "7221BE95-A362-460A-A2C3-AA0E8498A79D",
    # "sig": "97d916fc13c8a0df9f749aac0ff0e3c3",
    # "__NStokensig": "9588a766128ab1e7ba71be99fe2724d9776414ec076d602e5b920e40266c90c2",
    # "__NS_sig3": "9c8dfdde55f9daf9acd4d7d6cc0336b8f916aca6c9c5cbdd"
}



data = {
    'photoId': '5216857383469532746',
    'userId': '4808775036',
    'pageSource': '0',
    'displayType': '3',
    'client_key': '3c2cd3f3',
}


url = "https://api3.ksapisrv.com/rest/n/feed/selection/profile/position"


client_salt = '808e3d5a9fe2cf0e7342c0151d2fab85'

sig = sig_py(params, data)

print('sig' + '-->' + sig)

encurl = '/rest/n/feed/selection/profile/position' + sig

print('encurl' + '-->' + encurl)

sig3 = get_sig3(encurl)

print('sig3' + '-->' + sig3)

token = token_py(sig, client_salt)

print('__NStokensig-->' + token)

params['sig'] = sig
params['__NS_sig3'] = sig3
params['__NStokensig'] = token

response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data)

print(response.text)
print(response)

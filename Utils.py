# 作者 古月
# 开发时间 2025/5/25 21:38
# 文件名 Utils
import hashlib
import re
import subprocess
from functools import partial

subprocess.Popen = partial(subprocess.Popen, encoding='utf-8', errors='ignore')


def sig_py(QueryParmas: dict, DataParms: dict):
    # salt1 772867c19925
    # salt2 382700b563f4
    salt = b'382700b563f4'
    return hashlib.md5(''.join(sorted(
        f'{i[0]}={i[1]}' for i in sorted(QueryParmas.items()) + sorted(DataParms.items()))).encode() + salt).hexdigest()


def token_py(sig: str, Client_salt: str) -> str:
    return hashlib.sha256((sig + Client_salt).encode()).hexdigest()


def extract_signature(text):
    # 使用正则表达式匹配 `####` 中的内容
    match = re.search(r'##(.*?)##', text)

    if match:
        return match.group(1)  # 返回匹配的内容
    else:
        return None  # 如果没有找到匹配项，返回 None


def get_sig3(param):
    # 定义 unidbg 执行的命令
    global sig
    command = [
        'java',  # Java 命令
        '-jar',  # 使用 .jar 文件
        'ks_sig3.jar',  # unidbg .jar 文件的路径
        param  # 传入的参数
    ]

    try:
        # 调用 unidbg，并传入参数
        result = subprocess.run(command, stdout=subprocess.PIPE, text=True)

        # 获取 unidbg 输出
        output = result.stdout
        error = result.stderr

        if error:
            print(f"Error: {error}")
        else:
            # print(f"Output: {output}")
            sig_3 = extract_signature(output)

        return sig_3

    except subprocess.CalledProcessError as e:
        print(f"Error while calling unidbg: {e}")
        return None

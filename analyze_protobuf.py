import base64
import gzip

def analyze_protobuf_bytes(data):
    """分析protobuf字节数据"""
    print(f"数据长度: {len(data)} 字节")
    print(f"十六进制: {data.hex()}")
    print(f"字节数组: {list(data)}")
    
    # 尝试解析protobuf字段
    i = 0
    fields = []
    
    while i < len(data):
        if i >= len(data):
            break
            
        # 读取字段标识符
        field_byte = data[i]
        field_number = field_byte >> 3
        wire_type = field_byte & 0x07
        
        print(f"\n位置 {i}: 字段 {field_number}, 类型 {wire_type}")
        
        i += 1
        
        if wire_type == 0:  # Varint
            value = 0
            shift = 0
            while i < len(data):
                byte = data[i]
                value |= (byte & 0x7F) << shift
                i += 1
                if (byte & 0x80) == 0:
                    break
                shift += 7
            print(f"  Varint 值: {value}")
            fields.append((field_number, 'varint', value))
            
        elif wire_type == 2:  # Length-delimited
            # 读取长度
            length = 0
            shift = 0
            while i < len(data):
                byte = data[i]
                length |= (byte & 0x7F) << shift
                i += 1
                if (byte & 0x80) == 0:
                    break
                shift += 7
            
            # 读取数据
            if i + length <= len(data):
                field_data = data[i:i+length]
                i += length
                
                # 尝试解析为字符串
                try:
                    string_value = field_data.decode('utf-8')
                    print(f"  字符串 (长度 {length}): '{string_value}'")
                    fields.append((field_number, 'string', string_value))
                except:
                    print(f"  字节数据 (长度 {length}): {field_data.hex()}")
                    fields.append((field_number, 'bytes', field_data.hex()))
            else:
                print(f"  长度超出范围: {length}")
                break
        else:
            print(f"  未知类型: {wire_type}")
            i += 1
    
    return fields

def main():
    # 解密数据
    encrypted_data = "H4sIAAAAAAAA/+NQEmCQmPP2yIVvLEaSXOzFGYlFqZ4pQnyGFqYGBuZGhmYmJsYmpgDIFFGgJgAAAA=="
    
    print("=" * 60)
    print("解密并分析protobuf数据")
    print("=" * 60)
    
    try:
        # Base64解码
        compressed_data = base64.b64decode(encrypted_data)
        print(f"Base64解码后: {len(compressed_data)} 字节")
        
        # gzip解压
        decompressed_data = gzip.decompress(compressed_data)
        print(f"gzip解压后: {len(decompressed_data)} 字节")
        
        # 分析protobuf
        fields = analyze_protobuf_bytes(decompressed_data)
        
        print("\n" + "=" * 40)
        print("解析结果汇总:")
        print("=" * 40)
        
        for field_num, field_type, value in fields:
            print(f"字段 {field_num} ({field_type}): {value}")
        
        # 根据你提供的参考数据，尝试映射字段含义
        print("\n" + "=" * 40)
        print("可能的字段含义:")
        print("=" * 40)
        
        field_meanings = {
            1: "decode (boolean)",
            2: "bizType", 
            3: "photoId",
            4: "position",
            5: "extTag",
            6: "extInfoMap"
        }
        
        for field_num, field_type, value in fields:
            meaning = field_meanings.get(field_num, "未知字段")
            print(f"字段 {field_num}: {meaning} = {value}")
            
    except Exception as e:
        print(f"解析失败: {e}")

if __name__ == "__main__":
    main()
